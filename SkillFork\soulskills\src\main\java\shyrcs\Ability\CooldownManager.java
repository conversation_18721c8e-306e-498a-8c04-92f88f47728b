package shyrcs.Ability;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Quản lý cooldown và hiển thị trên ActionBar
 * Tương tự như MMOItems với hiển thị liên tục
 */
public class CooldownManager {
    
    private static CooldownManager instance;
    private final Plugin plugin;
    
    // Map lưu trữ cooldown của từng player cho từng skill
    private final Map<UUID, Map<String, Long>> playerCooldowns = new ConcurrentHashMap<>();
    
    // Map lưu trữ thời gian cooldown cho từng skill type
    private final Map<String, Integer> skillCooldowns = new HashMap<>();
    
    // Task hiển thị ActionBar
    private BukkitRunnable actionBarTask;
    
    private CooldownManager(Plugin plugin) {
        this.plugin = plugin;
        startActionBarTask();
    }
    
    public static CooldownManager getInstance(Plugin plugin) {
        if (instance == null) {
            instance = new CooldownManager(plugin);
        }
        return instance;
    }
    
    public static CooldownManager getInstance() {
        return instance;
    }
    
    /**
     * Đặt cooldown cho player với skill cụ thể
     */
    public void setCooldown(Player player, String skillType, int cooldownSeconds) {
        UUID playerId = player.getUniqueId();
        long cooldownEnd = System.currentTimeMillis() + (cooldownSeconds * 1000L);
        
        playerCooldowns.computeIfAbsent(playerId, k -> new ConcurrentHashMap<>())
                      .put(skillType, cooldownEnd);
        
        skillCooldowns.put(skillType, cooldownSeconds);
    }
    
    /**
     * Kiểm tra xem player có đang trong cooldown không
     */
    public boolean isOnCooldown(Player player, String skillType) {
        UUID playerId = player.getUniqueId();
        Map<String, Long> playerSkillCooldowns = playerCooldowns.get(playerId);
        
        if (playerSkillCooldowns == null) {
            return false;
        }
        
        Long cooldownEnd = playerSkillCooldowns.get(skillType);
        if (cooldownEnd == null) {
            return false;
        }
        
        long currentTime = System.currentTimeMillis();
        if (currentTime >= cooldownEnd) {
            // Cooldown đã hết, xóa khỏi map
            playerSkillCooldowns.remove(skillType);
            if (playerSkillCooldowns.isEmpty()) {
                playerCooldowns.remove(playerId);
            }
            return false;
        }
        
        return true;
    }
    
    /**
     * Lấy thời gian cooldown còn lại (giây)
     */
    public long getRemainingCooldown(Player player, String skillType) {
        UUID playerId = player.getUniqueId();
        Map<String, Long> playerSkillCooldowns = playerCooldowns.get(playerId);
        
        if (playerSkillCooldowns == null) {
            return 0;
        }
        
        Long cooldownEnd = playerSkillCooldowns.get(skillType);
        if (cooldownEnd == null) {
            return 0;
        }
        
        long currentTime = System.currentTimeMillis();
        long remaining = (cooldownEnd - currentTime) / 1000;
        
        return Math.max(0, remaining);
    }
    
    /**
     * Xóa tất cả cooldown của player
     */
    public void clearCooldowns(Player player) {
        playerCooldowns.remove(player.getUniqueId());
    }
    
    /**
     * Xóa cooldown cụ thể của player
     */
    public void clearCooldown(Player player, String skillType) {
        UUID playerId = player.getUniqueId();
        Map<String, Long> playerSkillCooldowns = playerCooldowns.get(playerId);
        
        if (playerSkillCooldowns != null) {
            playerSkillCooldowns.remove(skillType);
            if (playerSkillCooldowns.isEmpty()) {
                playerCooldowns.remove(playerId);
            }
        }
    }
    
    /**
     * Bắt đầu task hiển thị ActionBar
     */
    private void startActionBarTask() {
        if (actionBarTask != null) {
            actionBarTask.cancel();
        }
        
        actionBarTask = new BukkitRunnable() {
            @Override
            public void run() {
                for (Player player : Bukkit.getOnlinePlayers()) {
                    updatePlayerActionBar(player);
                }
            }
        };
        
        // Chạy mỗi 10 ticks (0.5 giây) để smooth
        actionBarTask.runTaskTimer(plugin, 0L, 10L);
    }
    
    /**
     * Cập nhật ActionBar cho player
     */
    private void updatePlayerActionBar(Player player) {
        UUID playerId = player.getUniqueId();
        Map<String, Long> playerSkillCooldowns = playerCooldowns.get(playerId);
        
        if (playerSkillCooldowns == null || playerSkillCooldowns.isEmpty()) {
            return;
        }
        
        StringBuilder actionBarText = new StringBuilder();
        boolean hasActiveCooldown = false;
        
        for (Map.Entry<String, Long> entry : playerSkillCooldowns.entrySet()) {
            String skillType = entry.getKey();
            long remaining = getRemainingCooldown(player, skillType);
            
            if (remaining > 0) {
                if (hasActiveCooldown) {
                    actionBarText.append(" §7| ");
                }
                
                String skillDisplayName = getSkillDisplayName(skillType);
                String timeFormat = BuffUtils.formatCooldownTime(remaining);
                actionBarText.append("§e").append(skillDisplayName).append(": ").append(timeFormat);
                hasActiveCooldown = true;
            }
        }
        
        if (hasActiveCooldown) {
            BuffUtils.sendActionBar(player, actionBarText.toString());
        }
    }
    
    /**
     * Lấy tên hiển thị của skill
     */
    private String getSkillDisplayName(String skillType) {
        switch (skillType.toLowerCase()) {
            case "buff":
                return "Buff";
            case "ability":
                return "Ability";
            case "skill":
                return "Skill";
            default:
                return skillType;
        }
    }
    
    /**
     * Dừng CooldownManager
     */
    public void shutdown() {
        if (actionBarTask != null) {
            actionBarTask.cancel();
            actionBarTask = null;
        }
        playerCooldowns.clear();
        skillCooldowns.clear();
    }
    
    /**
     * Lấy tất cả cooldown hiện tại của player (cho debug)
     */
    public Map<String, Long> getPlayerCooldowns(Player player) {
        Map<String, Long> cooldowns = playerCooldowns.get(player.getUniqueId());
        return cooldowns != null ? new HashMap<>(cooldowns) : new HashMap<>();
    }
}
